# System Architecture Document
## BAGEL Studio - Comprehensive AI Application Platform

### 1. Architecture Overview

#### 1.1 High-Level Architecture
BAGEL Studio follows a modular, microservices-inspired architecture with a desktop-first approach. The system is designed for local-first operation while supporting optional cloud integrations.

```
┌─────────────────────────────────────────────────────────────────┐
│                        BAGEL Studio                            │
├─────────────────────────────────────────────────────────────────┤
│  Frontend Layer (Electron + React)                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │    Chat     │ │  Knowledge  │ │   Model     │ │   Plugin    ││
│  │ Interface   │ │   Stack     │ │ Management  │ │  Manager    ││
│  │             │ │     UI      │ │     UI      │ │             ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
├─────────────────────────────────────────────────────────────────┤
│  API Gateway Layer (FastAPI)                                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │   Chat      │ │  Knowledge  │ │   Model     │ │   Plugin    ││
│  │    API      │ │  Stack API  │ │    API      │ │    API      ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
├─────────────────────────────────────────────────────────────────┤
│  Core Services Layer                                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │  Inference  │ │   Vector    │ │   Model     │ │   Computer  ││
│  │   Engine    │ │  Database   │ │  Registry   │ │   Vision    ││
│  │(llama.cpp)  │ │ (ChromaDB)  │ │             │ │   Engine    ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │   Plugin    │ │    OCR      │ │    Pose     │ │  Tracking   ││
│  │   Runtime   │ │   Engine    │ │ Estimation  │ │   System    ││
│  │             │ │(PaddleOCR)  │ │(MediaPipe)  │ │(DeepSORT)   ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
├─────────────────────────────────────────────────────────────────┤
│  Data Layer                                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │   SQLite    │ │   Vector    │ │   Model     │ │   Config    ││
│  │  Database   │ │   Store     │ │   Storage   │ │   Files     ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

#### 1.2 Architecture Principles
- **Local-First**: Core functionality works without internet connection
- **Modular Design**: Loosely coupled components for maintainability
- **Privacy-Focused**: User data remains on local device
- **Extensible**: Plugin architecture for custom functionality
- **Performance-Oriented**: Optimized for real-time AI interactions

### 2. Component Architecture

#### 2.1 Frontend Layer

##### 2.1.1 Native Technology Stack
- **Framework**: Qt 6 with C++ (Primary) / Tauri with Rust (Alternative)
- **Language**: C++17/20 for Qt, Rust for Tauri backend
- **UI Framework**: Qt Widgets/QML for modern UI
- **Graphics**: Qt Graphics Framework with hardware acceleration
- **Threading**: Qt Concurrent for background operations

##### 2.1.2 Native Application Structure
```
src/
├── main/
│   ├── main.cpp                    # Application entry point
│   ├── mainwindow.cpp/.h          # Main application window
│   └── application.cpp/.h         # Application class
├── widgets/
│   ├── chat/
│   │   ├── ChatWidget.cpp/.h      # Main chat interface
│   │   ├── MessageList.cpp/.h     # Message display widget
│   │   ├── InputArea.cpp/.h       # Message input widget
│   │   └── AttachmentHandler.cpp/.h # File attachment handling
│   ├── knowledge/
│   │   ├── StackManager.cpp/.h    # Knowledge stack management
│   │   ├── StackCreator.cpp/.h    # Stack creation wizard
│   │   └── DocumentViewer.cpp/.h  # Document preview widget
│   ├── models/
│   │   ├── ModelSelector.cpp/.h   # Model selection widget
│   │   ├── ModelDownloader.cpp/.h # Model download interface
│   │   └── ParameterControls.cpp/.h # Inference parameter controls
│   └── plugins/
│       ├── PluginManager.cpp/.h   # Plugin management interface
│       └── PluginStore.cpp/.h     # Plugin marketplace
├── managers/
│   ├── ChatManager.cpp/.h         # Chat session management
│   ├── ModelManager.cpp/.h        # AI model management
│   ├── KnowledgeManager.cpp/.h    # Knowledge stack management
│   └── PluginManager.cpp/.h       # Plugin system management
├── services/
│   ├── ApiService.cpp/.h          # Backend API communication
│   ├── FileService.cpp/.h         # File handling operations
│   └── ConfigService.cpp/.h       # Configuration management
└── resources/
    ├── icons/                     # Application icons
    ├── styles/                    # Qt stylesheets
    └── translations/              # Internationalization files
```

#### 2.2 API Gateway Layer

##### 2.2.1 FastAPI Application Structure
```python
app/
├── main.py                 # FastAPI application entry point
├── routers/
│   ├── chat.py            # Chat endpoints
│   ├── knowledge.py       # Knowledge stack endpoints
│   ├── models.py          # Model management endpoints
│   └── plugins.py         # Plugin management endpoints
├── middleware/
│   ├── auth.py            # Authentication middleware
│   ├── cors.py            # CORS configuration
│   └── logging.py         # Request logging
└── dependencies/
    ├── database.py        # Database connections
    └── services.py        # Service dependencies
```

##### 2.2.2 API Endpoints
```yaml
# Core Chat API
POST /api/v1/chat/completions     # OpenAI-compatible chat endpoint
GET  /api/v1/chat/history         # Conversation history
POST /api/v1/chat/regenerate      # Regenerate last response

# Knowledge Stack API
POST /api/v1/knowledge/stacks     # Create knowledge stack
GET  /api/v1/knowledge/stacks     # List knowledge stacks
POST /api/v1/knowledge/query      # Query knowledge stack
POST /api/v1/knowledge/upload     # Upload documents

# Model Management API
GET  /api/v1/models               # List available models
POST /api/v1/models/load          # Load model
POST /api/v1/models/unload        # Unload model
GET  /api/v1/models/status        # Model status

# Plugin API
GET  /api/v1/plugins              # List plugins
POST /api/v1/plugins/install      # Install plugin
POST /api/v1/plugins/enable       # Enable plugin
```

#### 2.3 Core Services Layer

##### 2.3.1 Inference Engine
```python
class InferenceEngine:
    """BAGEL model inference engine using llama.cpp"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.config = InferenceConfig()
    
    async def load_model(self, model_path: str) -> bool:
        """Load BAGEL model for inference"""
        pass
    
    async def generate(self, prompt: str, **kwargs) -> AsyncIterator[str]:
        """Generate streaming response"""
        pass
    
    async def generate_image(self, prompt: str, **kwargs) -> bytes:
        """Generate image from text prompt"""
        pass
    
    async def edit_image(self, image: bytes, instruction: str) -> bytes:
        """Edit image based on natural language instruction"""
        pass
```

##### 2.3.2 Knowledge Management Service
```python
class KnowledgeService:
    """Advanced RAG system with multi-source support"""
    
    def __init__(self):
        self.vector_db = ChromaDB()
        self.embedder = SentenceTransformer()
        self.reranker = CrossEncoder()
    
    async def create_stack(self, name: str, sources: List[DataSource]) -> Stack:
        """Create new knowledge stack"""
        pass
    
    async def query_stack(self, stack_id: str, query: str) -> List[Document]:
        """Query knowledge stack with semantic search"""
        pass
    
    async def add_documents(self, stack_id: str, documents: List[Document]):
        """Add documents to knowledge stack"""
        pass
```

##### 2.3.3 Model Registry Service
```python
class ModelRegistry:
    """Manage model lifecycle and metadata"""

    def __init__(self):
        self.models = {}
        self.active_model = None
        self.repositories = {}

    async def register_model(self, model_info: ModelInfo) -> str:
        """Register new model"""
        pass

    async def download_model(self, model_id: str) -> bool:
        """Download model from repository"""
        pass

    async def quantize_model(self, model_id: str, quantization: str) -> str:
        """Create quantized version of model"""
        pass
```

##### 2.3.4 Model Catalog Service
```python
class ModelCatalogService:
    """Unified model catalog across multiple repositories"""

    def __init__(self):
        self.repositories = {
            'huggingface': HuggingFaceAdapter(),
            'ollama': OllamaAdapter(),
            'civitai': CivitaiAdapter(),
            'tfhub': TensorFlowHubAdapter(),
            'pytorch': PyTorchHubAdapter()
        }
        self.search_index = SearchIndex()
        self.model_cache = ModelCache()

    async def search_models(self, query: str, filters: Dict) -> List[ModelInfo]:
        """Search across all repositories"""
        pass

    async def get_model_details(self, repository: str, model_id: str) -> ModelInfo:
        """Get detailed model information"""
        pass

    async def get_trending_models(self, category: str = None) -> List[ModelInfo]:
        """Get trending models across repositories"""
        pass

    async def compare_models(self, model_ids: List[str]) -> ModelComparison:
        """Compare multiple models side-by-side"""
        pass

    async def get_recommendations(self, user_context: Dict) -> List[ModelInfo]:
        """Get personalized model recommendations"""
        pass
```

##### 2.3.5 Repository Adapters
```python
class RepositoryAdapter:
    """Base class for repository integrations"""

    async def search_models(self, query: str, filters: Dict) -> List[ModelInfo]:
        """Search models in repository"""
        raise NotImplementedError

    async def get_model_details(self, model_id: str) -> ModelInfo:
        """Get detailed model information"""
        raise NotImplementedError

    async def download_model(self, model_id: str, format: str = None) -> str:
        """Download model file"""
        raise NotImplementedError

    async def get_categories(self) -> List[Category]:
        """Get available model categories"""
        raise NotImplementedError

class HuggingFaceAdapter(RepositoryAdapter):
    """Hugging Face Hub integration"""

    def __init__(self):
        self.base_url = "https://huggingface.co/api"
        self.session = aiohttp.ClientSession()

    async def search_models(self, query: str, filters: Dict) -> List[ModelInfo]:
        """Search Hugging Face models"""
        params = {
            'search': query,
            'filter': filters.get('tags'),
            'sort': filters.get('sort', 'downloads'),
            'limit': filters.get('limit', 20)
        }
        # Implementation details...
        pass

class OllamaAdapter(RepositoryAdapter):
    """Ollama registry integration"""

    def __init__(self):
        self.base_url = "https://ollama.com/api"
        self.session = aiohttp.ClientSession()

    async def search_models(self, query: str, filters: Dict) -> List[ModelInfo]:
        """Search Ollama models"""
        # Implementation for Ollama-specific API
        pass

class CivitaiAdapter(RepositoryAdapter):
    """Civitai integration"""

    def __init__(self):
        self.base_url = "https://civitai.com/api/v1"
        self.session = aiohttp.ClientSession()

    async def search_models(self, query: str, filters: Dict) -> List[ModelInfo]:
        """Search Civitai models"""
        # Implementation for Civitai API
        pass
```

### 3. Data Architecture

#### 3.1 Database Schema

##### 3.1.1 SQLite Schema
```sql
-- Conversations and messages
CREATE TABLE conversations (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    model_id TEXT,
    settings JSON
);

CREATE TABLE messages (
    id TEXT PRIMARY KEY,
    conversation_id TEXT REFERENCES conversations(id),
    role TEXT NOT NULL, -- 'user', 'assistant', 'system'
    content TEXT NOT NULL,
    attachments JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Knowledge stacks
CREATE TABLE knowledge_stacks (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE documents (
    id TEXT PRIMARY KEY,
    stack_id TEXT REFERENCES knowledge_stacks(id),
    filename TEXT NOT NULL,
    content_type TEXT,
    metadata JSON,
    processed_at TIMESTAMP
);

-- Models
CREATE TABLE models (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    path TEXT NOT NULL,
    type TEXT, -- 'base', 'custom', 'fine-tuned'
    size_bytes INTEGER,
    parameters JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### 3.1.2 Vector Database Schema
```python
# ChromaDB collections
collections = {
    "knowledge_stacks": {
        "embedding_function": "sentence-transformers",
        "metadata_fields": ["stack_id", "document_id", "chunk_index", "source"]
    },
    "conversations": {
        "embedding_function": "sentence-transformers", 
        "metadata_fields": ["conversation_id", "message_id", "timestamp"]
    }
}
```

#### 3.2 File System Organization
```
~/.bagel-studio/
├── config/
│   ├── app.json           # Application configuration
│   ├── models.json        # Model registry
│   └── plugins.json       # Plugin configuration
├── data/
│   ├── database.db        # SQLite database
│   ├── vector_db/         # ChromaDB storage
│   └── cache/             # Temporary cache files
├── models/
│   ├── bagel-7b-q4.gguf   # Quantized BAGEL model
│   ├── custom/            # Custom models
│   └── embeddings/        # Embedding models
├── knowledge/
│   ├── stacks/            # Knowledge stack data
│   └── uploads/           # Uploaded documents
└── plugins/
    ├── installed/         # Installed plugins
    └── cache/             # Plugin cache
```

### 4. Security Architecture

#### 4.1 Security Layers
- **Application Security**: Code signing and integrity verification
- **Data Security**: AES-256 encryption for sensitive data
- **Model Security**: Checksum verification and sandboxed execution
- **Plugin Security**: Isolated execution environment with limited permissions

#### 4.2 Privacy Protection
- **Local Processing**: All AI inference happens locally
- **Data Isolation**: User data never leaves the device without explicit consent
- **Audit Logging**: Comprehensive logging of data access and processing
- **Secure Deletion**: Proper cleanup of temporary files and cache

### 5. Performance Architecture

#### 5.1 Optimization Strategies
- **Model Quantization**: Automatic selection of optimal quantization level
- **Memory Management**: Intelligent model loading/unloading based on usage
- **Caching**: Multi-level caching for embeddings, responses, and metadata
- **Batching**: Dynamic request batching for improved throughput

#### 5.2 Scalability Considerations
- **Horizontal Scaling**: Support for multiple model instances
- **Resource Pooling**: Shared resources across conversations
- **Load Balancing**: Intelligent request distribution
- **Background Processing**: Asynchronous document processing

### 6. Integration Architecture

#### 6.1 Plugin System
```python
class PluginInterface:
    """Base interface for all plugins"""
    
    def initialize(self, context: PluginContext) -> bool:
        """Initialize plugin with application context"""
        pass
    
    def process_message(self, message: Message) -> Message:
        """Process chat message"""
        pass
    
    def add_ui_components(self) -> List[UIComponent]:
        """Add custom UI components"""
        pass
```

#### 6.2 External Integrations
- **Model Repositories**: Hugging Face, Ollama registry
- **Cloud Services**: Optional OpenAI API, Anthropic Claude
- **File Systems**: Local file system, cloud storage (optional)
- **Web Services**: YouTube API, web scraping capabilities

### 7. Deployment Architecture

#### 7.1 Desktop Application
- **Packaging**: Electron Builder for cross-platform packages
- **Distribution**: Direct download, package managers (Homebrew, Chocolatey)
- **Updates**: Auto-updater with delta updates
- **Installation**: Silent installation with minimal user interaction

#### 7.2 Development Environment
- **Development**: Hot reload for frontend, auto-restart for backend
- **Testing**: Automated testing pipeline with CI/CD
- **Building**: Automated build process with artifact generation
- **Deployment**: Automated release pipeline with version management
