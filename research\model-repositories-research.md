# Model Repositories and Catalogs Research

## Overview
This document provides comprehensive research on major AI model repositories and their APIs to support the implementation of a unified model catalog and management system in BAGEL Studio.

## Major Model Repositories

### 1. Hugging Face Hub
**URL**: https://huggingface.co/models
**Models**: 1,800,541+ models (as of research date)

#### API Capabilities
- **Base URL**: `https://huggingface.co/api/`
- **Authentication**: API tokens via Authorization header or query parameter
- **Rate Limiting**: Generous limits for public API

#### Key Endpoints
```
GET /api/models                    # List all models with filtering
GET /api/models/{repo_id}          # Get specific model details
GET /api/models-tags-by-type       # Get all available model tags
```

#### Search and Filter Parameters
- **search**: Text search across model names and descriptions
- **author**: Filter by creator/organization
- **filter**: Tag-based filtering (e.g., `text-classification`, `image-generation`)
- **sort**: Sort by downloads, trending, newest, etc.
- **limit**: Results per page (1-100)
- **full**: Include complete metadata

#### Model Categories
- **Text Models**: GPT, BERT, T5, LLaMA variants
- **Image Models**: Stable Diffusion, DALL-E variants, ControlNet
- **Audio Models**: Whisper, MusicGen, AudioCraft
- **Multimodal**: CLIP, BLIP, LLaVA, BAGEL
- **Code Models**: CodeT5, StarCoder, CodeLlama
- **Specialized**: Medical, Scientific, Domain-specific

#### Metadata Available
- Model type, size, license, downloads
- Tags, languages, datasets used
- Performance metrics, benchmarks
- Model card with detailed descriptions
- Files (weights, configs, tokenizers)

### 2. Ollama Registry
**URL**: https://ollama.com/library
**Models**: 100+ curated models

#### API Capabilities
- **Base URL**: `https://ollama.com/api/`
- **Focus**: Local inference optimized models
- **Format**: GGUF quantized models

#### Model Categories
- **Language Models**: Llama, Mistral, CodeLlama, Gemma
- **Code Models**: CodeLlama, StarCoder, DeepSeek-Coder
- **Embedding Models**: nomic-embed, all-minilm
- **Vision Models**: LLaVA, BakLLaVA
- **Specialized**: Medical, Math, Reasoning models

#### Unique Features
- **Quantization Levels**: Q4_0, Q4_1, Q5_0, Q5_1, Q8_0
- **Model Variants**: Different parameter sizes (7B, 13B, 70B)
- **Optimized for Local**: CPU and GPU inference ready
- **Modelfile Support**: Custom model creation

### 3. Civitai
**URL**: https://civitai.com/
**Models**: 100,000+ community models

#### API Capabilities
- **Base URL**: `https://civitai.com/api/v1/`
- **Authentication**: API key required for some features
- **Focus**: Image generation and creative AI models

#### Key Endpoints
```
GET /api/v1/models                 # List models with filtering
GET /api/v1/models/{id}            # Get specific model
GET /api/v1/creators               # List model creators
GET /api/v1/tags                   # Get available tags
GET /api/v1/images                 # Browse generated images
```

#### Model Categories
- **Checkpoints**: Base Stable Diffusion models
- **LoRA**: Low-rank adaptation models
- **Textual Inversions**: Embedding-based models
- **ControlNet**: Conditional generation models
- **Hypernetworks**: Style transfer models
- **Poses**: Human pose models

#### Search and Filter Features
- **NSFW Filtering**: Content safety controls
- **License Filtering**: Commercial use permissions
- **Quality Metrics**: Community ratings and downloads
- **Creator Filtering**: Filter by specific artists/creators

### 4. TensorFlow Hub
**URL**: https://tfhub.dev/
**Models**: 4,000+ models

#### API Capabilities
- **Base URL**: `https://tfhub.dev/`
- **Format**: TensorFlow SavedModel format
- **Integration**: Direct TensorFlow/Keras integration

#### Model Categories
- **Text**: BERT, Universal Sentence Encoder, T5
- **Image**: MobileNet, ResNet, EfficientNet
- **Video**: Action recognition, video classification
- **Audio**: Speech recognition, audio classification

#### Unique Features
- **Module System**: Reusable model components
- **Fine-tuning Ready**: Pre-trained models for transfer learning
- **Mobile Optimized**: TensorFlow Lite compatible models

### 5. PyTorch Hub
**URL**: https://pytorch.org/hub/
**Models**: 500+ research models

#### API Capabilities
- **Integration**: Direct PyTorch integration
- **Format**: PyTorch model format
- **Research Focus**: Cutting-edge research models

#### Model Categories
- **Vision**: ResNet, DenseNet, MobileNet
- **NLP**: BERT, RoBERTa, DistilBERT
- **Audio**: Wav2Vec, DeepSpeech
- **Generative**: StyleGAN, DCGAN

### 6. Model Zoo Repositories

#### Papers with Code Model Index
- **URL**: https://paperswithcode.com/
- **Models**: 50,000+ research models
- **Focus**: Academic research models with papers

#### ONNX Model Zoo
- **URL**: https://github.com/onnx/models
- **Models**: 100+ optimized models
- **Format**: ONNX format for cross-platform inference

#### OpenMMLab Model Zoo
- **URL**: https://openmmlab.com/
- **Models**: 2,000+ computer vision models
- **Focus**: Computer vision research and applications

## Model Catalog Requirements

### 1. Unified Search Interface
- **Cross-repository Search**: Search across all repositories simultaneously
- **Advanced Filtering**: By model type, size, license, performance
- **Semantic Search**: Find models by capability description
- **Tag-based Discovery**: Browse by standardized tags

### 2. Model Metadata Standardization
```json
{
  "id": "unique_model_identifier",
  "name": "Model Display Name",
  "description": "Detailed model description",
  "repository": "huggingface|ollama|civitai|tfhub|pytorch",
  "author": "Creator/Organization",
  "license": "apache-2.0|mit|cc-by-4.0|custom",
  "type": "text-generation|image-generation|embedding|classification",
  "architecture": "transformer|diffusion|cnn|rnn",
  "parameters": "7B|13B|70B|unknown",
  "size_mb": 4096,
  "tags": ["nlp", "chat", "instruction-following"],
  "languages": ["en", "es", "fr"],
  "performance": {
    "benchmark": "score",
    "inference_speed": "tokens/sec"
  },
  "downloads": 1000000,
  "rating": 4.8,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-06-01T00:00:00Z",
  "files": [
    {
      "filename": "model.safetensors",
      "size_mb": 4096,
      "format": "safetensors|gguf|pytorch|onnx",
      "quantization": "fp16|int8|int4|none"
    }
  ],
  "requirements": {
    "memory_gb": 8,
    "gpu_memory_gb": 4,
    "frameworks": ["pytorch", "transformers"]
  }
}
```

### 3. Category Taxonomy
```
Text Models/
├── Language Models/
│   ├── Chat & Instruction/
│   ├── Code Generation/
│   ├── Creative Writing/
│   └── Reasoning & Math/
├── Embeddings/
│   ├── Sentence Embeddings/
│   ├── Document Embeddings/
│   └── Code Embeddings/
└── Specialized/
    ├── Translation/
    ├── Summarization/
    └── Question Answering/

Image Models/
├── Generation/
│   ├── Text-to-Image/
│   ├── Image-to-Image/
│   └── Inpainting/
├── Understanding/
│   ├── Classification/
│   ├── Object Detection/
│   └── Segmentation/
└── Editing/
    ├── Style Transfer/
    ├── Super Resolution/
    └── Restoration/

Multimodal Models/
├── Vision-Language/
│   ├── Image Captioning/
│   ├── Visual Q&A/
│   └── Image-Text Retrieval/
├── Audio-Text/
│   ├── Speech Recognition/
│   ├── Text-to-Speech/
│   └── Audio Captioning/
└── Video Understanding/
    ├── Video Captioning/
    ├── Action Recognition/
    └── Video Q&A/
```

### 4. Advanced Search Features
- **Capability-based Search**: "Find models that can generate images from text"
- **Performance Filtering**: Filter by inference speed, accuracy, model size
- **Hardware Requirements**: Filter by GPU memory, CPU requirements
- **License Compatibility**: Filter by commercial use permissions
- **Language Support**: Filter by supported languages
- **Benchmark Scores**: Sort by performance on standard benchmarks

### 5. Model Comparison Features
- **Side-by-side Comparison**: Compare multiple models across metrics
- **Performance Benchmarks**: Standardized evaluation results
- **Resource Requirements**: Memory, compute, storage needs
- **Use Case Recommendations**: Suggest best models for specific tasks

### 6. Integration Features
- **One-click Download**: Direct download and installation
- **Format Conversion**: Automatic conversion between formats
- **Quantization Options**: On-demand model quantization
- **Dependency Management**: Automatic framework installation

## Implementation Architecture

### 1. Repository Adapters
```python
class RepositoryAdapter:
    def search_models(self, query: str, filters: Dict) -> List[ModelInfo]
    def get_model_details(self, model_id: str) -> ModelInfo
    def download_model(self, model_id: str, format: str) -> str
    def get_categories(self) -> List[Category]
    def get_tags(self) -> List[Tag]

class HuggingFaceAdapter(RepositoryAdapter):
    # Implementation for Hugging Face API

class OllamaAdapter(RepositoryAdapter):
    # Implementation for Ollama API

class CivitaiAdapter(RepositoryAdapter):
    # Implementation for Civitai API
```

### 2. Unified Model Index
- **Local Database**: Cache model metadata for fast search
- **Periodic Sync**: Regular updates from all repositories
- **Search Index**: Elasticsearch/OpenSearch for advanced search
- **Recommendation Engine**: ML-based model recommendations

### 3. Download Management
- **Queue System**: Manage multiple concurrent downloads
- **Progress Tracking**: Real-time download progress
- **Resume Support**: Resume interrupted downloads
- **Storage Management**: Automatic cleanup and organization

### 4. Model Verification
- **Checksum Validation**: Verify file integrity
- **Security Scanning**: Malware and pickle scanning
- **License Compliance**: Verify license compatibility
- **Performance Testing**: Benchmark model performance

## User Experience Features

### 1. Discovery Interface
- **Trending Models**: Popular and recently updated models
- **Curated Collections**: Hand-picked model collections
- **Personalized Recommendations**: Based on usage history
- **Community Ratings**: User reviews and ratings

### 2. Model Management
- **Local Library**: Manage downloaded models
- **Version Control**: Track model versions and updates
- **Usage Analytics**: Track model performance and usage
- **Backup and Sync**: Cloud backup of model configurations

### 3. Integration Workflows
- **Quick Setup**: One-click model setup for common tasks
- **Custom Pipelines**: Create custom model pipelines
- **A/B Testing**: Compare model performance
- **Deployment Tools**: Deploy models to production

## Technical Implementation Architecture

### 1. Repository Adapter Pattern Implementation

#### Base Repository Interface
```cpp
// C++ interface for repository adapters
class IRepositoryAdapter {
public:
    virtual ~IRepositoryAdapter() = default;

    virtual std::future<std::vector<ModelInfo>> searchModels(
        const SearchQuery& query,
        const SearchFilters& filters
    ) = 0;

    virtual std::future<ModelDetails> getModelDetails(
        const std::string& model_id
    ) = 0;

    virtual std::future<DownloadInfo> getDownloadInfo(
        const std::string& model_id,
        const std::string& format = ""
    ) = 0;

    virtual std::future<std::vector<Category>> getCategories() = 0;
    virtual std::future<std::vector<Tag>> getTags() = 0;

    virtual std::string getRepositoryName() const = 0;
    virtual std::string getBaseUrl() const = 0;
    virtual bool requiresAuthentication() const = 0;
};
```

#### Hugging Face Adapter Implementation
```python
class HuggingFaceAdapter:
    def __init__(self, api_key: Optional[str] = None):
        self.base_url = "https://huggingface.co/api"
        self.api_key = api_key
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers=self._get_headers()
        )
        self.rate_limiter = AsyncRateLimiter(1000, 3600)  # 1000 requests per hour
        self.cache = TTLCache(maxsize=10000, ttl=3600)  # 1 hour cache

    async def search_models(self,
                          query: str,
                          filters: Dict[str, Any],
                          limit: int = 20,
                          offset: int = 0) -> List[ModelInfo]:
        """Search Hugging Face models with advanced filtering"""

        # Build search parameters
        params = {
            'search': query,
            'limit': limit,
            'skip': offset,
            'sort': filters.get('sort', 'downloads'),
            'direction': filters.get('direction', -1)
        }

        # Add filters
        if filters.get('task'):
            params['filter'] = f"task:{filters['task']}"

        if filters.get('library'):
            params['library'] = filters['library']

        if filters.get('language'):
            params['language'] = filters['language']

        # Apply rate limiting
        await self.rate_limiter.acquire()

        # Check cache first
        cache_key = self._generate_cache_key('search', params)
        if cache_key in self.cache:
            return self.cache[cache_key]

        try:
            async with self.session.get(f"{self.base_url}/models", params=params) as response:
                response.raise_for_status()
                data = await response.json()

                # Convert to standardized format
                models = [self._convert_hf_model(model) for model in data]

                # Cache results
                self.cache[cache_key] = models

                return models

        except aiohttp.ClientError as e:
            logger.error(f"HuggingFace API error: {e}")
            raise RepositoryError(f"Failed to search models: {e}")

    def _convert_hf_model(self, hf_model: Dict[str, Any]) -> ModelInfo:
        """Convert HuggingFace model format to standardized ModelInfo"""
        return ModelInfo(
            id=hf_model['id'],
            name=hf_model['id'].split('/')[-1],
            description=hf_model.get('description', ''),
            repository='huggingface',
            author=hf_model['id'].split('/')[0],
            license=hf_model.get('license', 'unknown'),
            model_type=self._extract_model_type(hf_model.get('tags', [])),
            architecture=self._extract_architecture(hf_model.get('tags', [])),
            parameters=self._extract_parameters(hf_model.get('tags', [])),
            size_mb=self._estimate_size(hf_model.get('siblings', [])),
            tags=hf_model.get('tags', []),
            languages=self._extract_languages(hf_model.get('tags', [])),
            downloads=hf_model.get('downloads', 0),
            created_at=self._parse_date(hf_model.get('createdAt')),
            updated_at=self._parse_date(hf_model.get('lastModified')),
            files=self._extract_files(hf_model.get('siblings', []))
        )

    async def download_model(self,
                           model_id: str,
                           target_format: str = None,
                           progress_callback: Callable = None) -> str:
        """Download model with progress tracking and format conversion"""

        # Get model details
        model_details = await self.get_model_details(model_id)

        # Find best file to download
        target_file = self._select_best_file(model_details.files, target_format)

        if not target_file:
            raise RepositoryError(f"No suitable file found for model {model_id}")

        # Create download URL
        download_url = f"https://huggingface.co/{model_id}/resolve/main/{target_file.filename}"

        # Download with progress tracking
        local_path = await self._download_with_progress(
            download_url,
            target_file.filename,
            progress_callback
        )

        # Convert format if needed
        if target_format and target_file.format != target_format:
            converted_path = await self._convert_model_format(
                local_path,
                target_file.format,
                target_format
            )
            return converted_path

        return local_path

    async def _download_with_progress(self,
                                    url: str,
                                    filename: str,
                                    progress_callback: Callable = None) -> str:
        """Download file with progress tracking"""

        local_path = os.path.join(self.download_dir, filename)

        async with self.session.get(url) as response:
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0

            with open(local_path, 'wb') as f:
                async for chunk in response.content.iter_chunked(8192):
                    f.write(chunk)
                    downloaded += len(chunk)

                    if progress_callback:
                        progress = downloaded / total_size if total_size > 0 else 0
                        await progress_callback(progress, downloaded, total_size)

        return local_path
```

### 2. Unified Search Engine Implementation

#### Elasticsearch Integration
```python
class ModelSearchEngine:
    def __init__(self, elasticsearch_url: str = "http://localhost:9200"):
        self.es_client = AsyncElasticsearch([elasticsearch_url])
        self.index_name = "ai_models"
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

    async def initialize_index(self):
        """Initialize Elasticsearch index with proper mappings"""

        mapping = {
            "mappings": {
                "properties": {
                    "id": {"type": "keyword"},
                    "name": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {
                            "keyword": {"type": "keyword"},
                            "suggest": {"type": "completion"}
                        }
                    },
                    "description": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "repository": {"type": "keyword"},
                    "author": {"type": "keyword"},
                    "license": {"type": "keyword"},
                    "model_type": {"type": "keyword"},
                    "architecture": {"type": "keyword"},
                    "parameters": {"type": "keyword"},
                    "size_mb": {"type": "long"},
                    "tags": {"type": "keyword"},
                    "languages": {"type": "keyword"},
                    "downloads": {"type": "long"},
                    "rating": {"type": "float"},
                    "created_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "embedding": {
                        "type": "dense_vector",
                        "dims": 384  # MiniLM embedding dimension
                    },
                    "performance_metrics": {
                        "type": "object",
                        "properties": {
                            "accuracy": {"type": "float"},
                            "speed": {"type": "float"},
                            "memory_usage": {"type": "long"}
                        }
                    }
                }
            }
        }

        # Create index if it doesn't exist
        if not await self.es_client.indices.exists(index=self.index_name):
            await self.es_client.indices.create(
                index=self.index_name,
                body=mapping
            )

    async def index_model(self, model: ModelInfo):
        """Index a model for search"""

        # Generate embedding for semantic search
        text_for_embedding = f"{model.name} {model.description} {' '.join(model.tags)}"
        embedding = self.embedding_model.encode(text_for_embedding).tolist()

        # Prepare document
        doc = {
            "id": model.id,
            "name": model.name,
            "description": model.description,
            "repository": model.repository,
            "author": model.author,
            "license": model.license,
            "model_type": model.model_type,
            "architecture": model.architecture,
            "parameters": model.parameters,
            "size_mb": model.size_mb,
            "tags": model.tags,
            "languages": model.languages,
            "downloads": model.downloads,
            "rating": model.rating,
            "created_at": model.created_at,
            "updated_at": model.updated_at,
            "embedding": embedding,
            "performance_metrics": model.performance_metrics
        }

        # Index document
        await self.es_client.index(
            index=self.index_name,
            id=model.id,
            body=doc
        )

    async def search_models(self,
                          query: str,
                          filters: Dict[str, Any],
                          sort_by: str = "downloads",
                          limit: int = 20,
                          offset: int = 0) -> SearchResults:
        """Advanced model search with semantic and keyword matching"""

        # Build Elasticsearch query
        es_query = {
            "query": {
                "bool": {
                    "must": [],
                    "filter": [],
                    "should": []
                }
            },
            "sort": self._build_sort(sort_by),
            "from": offset,
            "size": limit,
            "highlight": {
                "fields": {
                    "name": {},
                    "description": {}
                }
            }
        }

        # Add text search
        if query:
            # Keyword search
            es_query["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": query,
                    "fields": ["name^3", "description^2", "tags^1.5", "author"],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })

            # Semantic search
            query_embedding = self.embedding_model.encode(query).tolist()
            es_query["query"]["bool"]["should"].append({
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_embedding}
                    }
                }
            })

        # Add filters
        for field, value in filters.items():
            if field in ['repository', 'author', 'license', 'model_type', 'architecture']:
                if isinstance(value, list):
                    es_query["query"]["bool"]["filter"].append({
                        "terms": {field: value}
                    })
                else:
                    es_query["query"]["bool"]["filter"].append({
                        "term": {field: value}
                    })
            elif field == 'size_range':
                es_query["query"]["bool"]["filter"].append({
                    "range": {
                        "size_mb": {
                            "gte": value.get("min", 0),
                            "lte": value.get("max", float('inf'))
                        }
                    }
                })
            elif field == 'date_range':
                es_query["query"]["bool"]["filter"].append({
                    "range": {
                        "updated_at": {
                            "gte": value.get("from"),
                            "lte": value.get("to")
                        }
                    }
                })

        # Execute search
        response = await self.es_client.search(
            index=self.index_name,
            body=es_query
        )

        # Parse results
        models = []
        for hit in response['hits']['hits']:
            model = ModelInfo.from_dict(hit['_source'])
            model.search_score = hit['_score']
            model.highlights = hit.get('highlight', {})
            models.append(model)

        return SearchResults(
            models=models,
            total=response['hits']['total']['value'],
            query=query,
            filters=filters,
            took=response['took']
        )
```

### 3. Download Management System

#### Advanced Download Manager
```python
class DownloadManager:
    def __init__(self, max_concurrent_downloads: int = 3):
        self.max_concurrent = max_concurrent_downloads
        self.active_downloads = {}
        self.download_queue = asyncio.Queue()
        self.download_semaphore = asyncio.Semaphore(max_concurrent_downloads)
        self.storage_manager = StorageManager()
        self.format_converter = FormatConverter()

    async def start_download_workers(self):
        """Start background download workers"""
        for i in range(self.max_concurrent):
            asyncio.create_task(self._download_worker(f"worker-{i}"))

    async def _download_worker(self, worker_id: str):
        """Background worker for processing downloads"""
        while True:
            try:
                download_task = await self.download_queue.get()

                async with self.download_semaphore:
                    await self._process_download(download_task)

                self.download_queue.task_done()

            except Exception as e:
                logger.error(f"Download worker {worker_id} error: {e}")
                await asyncio.sleep(1)

    async def queue_download(self,
                           model_id: str,
                           repository: str,
                           target_format: str = None,
                           priority: int = 0) -> str:
        """Queue a model download"""

        download_id = f"{repository}:{model_id}:{int(time.time())}"

        download_task = DownloadTask(
            id=download_id,
            model_id=model_id,
            repository=repository,
            target_format=target_format,
            priority=priority,
            status=DownloadStatus.QUEUED,
            progress=0.0,
            created_at=datetime.utcnow()
        )

        # Add to active downloads tracking
        self.active_downloads[download_id] = download_task

        # Queue for processing
        await self.download_queue.put(download_task)

        return download_id

    async def _process_download(self, task: DownloadTask):
        """Process a download task"""
        try:
            task.status = DownloadStatus.DOWNLOADING
            task.started_at = datetime.utcnow()

            # Get repository adapter
            adapter = self._get_repository_adapter(task.repository)

            # Check available storage
            model_details = await adapter.get_model_details(task.model_id)
            required_space = sum(f.size_mb for f in model_details.files) * 1024 * 1024

            if not await self.storage_manager.ensure_space(required_space):
                raise DownloadError("Insufficient storage space")

            # Download model
            local_path = await adapter.download_model(
                task.model_id,
                target_format=task.target_format,
                progress_callback=lambda p, d, t: self._update_progress(task.id, p, d, t)
            )

            # Verify download
            if await self._verify_download(local_path, model_details):
                task.status = DownloadStatus.COMPLETED
                task.local_path = local_path
                task.completed_at = datetime.utcnow()

                # Update model registry
                await self._register_downloaded_model(task, model_details)

            else:
                raise DownloadError("Download verification failed")

        except Exception as e:
            task.status = DownloadStatus.FAILED
            task.error_message = str(e)
            logger.error(f"Download failed for {task.model_id}: {e}")

    async def _verify_download(self,
                             local_path: str,
                             model_details: ModelDetails) -> bool:
        """Verify downloaded model integrity"""

        # Check file size
        actual_size = os.path.getsize(local_path)
        expected_size = sum(f.size_mb for f in model_details.files) * 1024 * 1024

        if abs(actual_size - expected_size) > expected_size * 0.01:  # 1% tolerance
            return False

        # Check checksums if available
        for file_info in model_details.files:
            if file_info.checksum:
                calculated_checksum = await self._calculate_checksum(local_path)
                if calculated_checksum != file_info.checksum:
                    return False

        # Security scan
        if not await self._security_scan(local_path):
            return False

        return True

    async def _security_scan(self, file_path: str) -> bool:
        """Perform security scan on downloaded model"""

        # Check for pickle files (potential security risk)
        if file_path.endswith('.pkl') or file_path.endswith('.pickle'):
            # Use safe pickle loading to check for malicious content
            try:
                import pickle
                import io

                class SafeUnpickler(pickle.Unpickler):
                    def find_class(self, module, name):
                        # Only allow safe modules
                        safe_modules = ['torch', 'numpy', 'transformers']
                        if module.split('.')[0] not in safe_modules:
                            raise pickle.UnpicklingError(f"Unsafe module: {module}")
                        return super().find_class(module, name)

                with open(file_path, 'rb') as f:
                    SafeUnpickler(f).load()

            except Exception as e:
                logger.warning(f"Security scan failed for {file_path}: {e}")
                return False

        # Check file size limits
        max_size = 50 * 1024 * 1024 * 1024  # 50GB limit
        if os.path.getsize(file_path) > max_size:
            logger.warning(f"File too large: {file_path}")
            return False

        return True
```

### 4. Model Format Conversion

#### Universal Format Converter
```python
class FormatConverter:
    def __init__(self):
        self.converters = {
            ('pytorch', 'gguf'): self._pytorch_to_gguf,
            ('safetensors', 'gguf'): self._safetensors_to_gguf,
            ('gguf', 'pytorch'): self._gguf_to_pytorch,
            ('pytorch', 'onnx'): self._pytorch_to_onnx,
            ('onnx', 'pytorch'): self._onnx_to_pytorch
        }

    async def convert_model(self,
                          input_path: str,
                          source_format: str,
                          target_format: str,
                          model_config: Dict[str, Any] = None) -> str:
        """Convert model between formats"""

        converter_key = (source_format.lower(), target_format.lower())

        if converter_key not in self.converters:
            raise ConversionError(f"Conversion from {source_format} to {target_format} not supported")

        converter_func = self.converters[converter_key]

        # Generate output path
        output_path = self._generate_output_path(input_path, target_format)

        # Run conversion in thread pool
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            None,
            converter_func,
            input_path,
            output_path,
            model_config or {}
        )

        return output_path

    def _pytorch_to_gguf(self,
                        input_path: str,
                        output_path: str,
                        config: Dict[str, Any]):
        """Convert PyTorch model to GGUF format"""

        # This would use llama.cpp conversion tools
        import subprocess

        cmd = [
            "python", "-m", "llama_cpp.convert",
            "--input", input_path,
            "--output", output_path,
            "--type", config.get("quantization", "f16")
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            raise ConversionError(f"GGUF conversion failed: {result.stderr}")

    def _pytorch_to_onnx(self,
                        input_path: str,
                        output_path: str,
                        config: Dict[str, Any]):
        """Convert PyTorch model to ONNX format"""

        import torch
        import torch.onnx

        # Load PyTorch model
        model = torch.load(input_path, map_location='cpu')
        model.eval()

        # Create dummy input
        input_shape = config.get("input_shape", (1, 3, 224, 224))
        dummy_input = torch.randn(*input_shape)

        # Export to ONNX
        torch.onnx.export(
            model,
            dummy_input,
            output_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )
```

### 5. Caching and Performance Optimization

#### Multi-Level Caching System
```python
class ModelCacheManager:
    def __init__(self):
        # L1: In-memory cache for frequently accessed metadata
        self.memory_cache = TTLCache(maxsize=1000, ttl=3600)

        # L2: Redis cache for distributed caching
        self.redis_client = aioredis.from_url("redis://localhost:6379")

        # L3: Local disk cache for model files
        self.disk_cache = DiskCache(
            directory="cache/models",
            size_limit=100 * 1024 * 1024 * 1024  # 100GB
        )

        # Cache statistics
        self.stats = CacheStats()

    async def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get model info with multi-level caching"""

        # L1: Check memory cache
        if model_id in self.memory_cache:
            self.stats.memory_hits += 1
            return self.memory_cache[model_id]

        # L2: Check Redis cache
        redis_key = f"model:{model_id}"
        cached_data = await self.redis_client.get(redis_key)

        if cached_data:
            self.stats.redis_hits += 1
            model_info = ModelInfo.from_json(cached_data)

            # Store in L1 cache
            self.memory_cache[model_id] = model_info

            return model_info

        # L3: Not in cache, will need to fetch from repository
        self.stats.cache_misses += 1
        return None

    async def cache_model_info(self, model_info: ModelInfo):
        """Cache model info at all levels"""

        # L1: Memory cache
        self.memory_cache[model_info.id] = model_info

        # L2: Redis cache
        redis_key = f"model:{model_info.id}"
        await self.redis_client.setex(
            redis_key,
            3600,  # 1 hour TTL
            model_info.to_json()
        )

    async def get_cached_model_file(self, model_id: str, filename: str) -> Optional[str]:
        """Get cached model file path"""

        cache_key = f"{model_id}/{filename}"

        if cache_key in self.disk_cache:
            self.stats.file_cache_hits += 1
            return self.disk_cache.get_path(cache_key)

        self.stats.file_cache_misses += 1
        return None

    async def cache_model_file(self,
                             model_id: str,
                             filename: str,
                             file_path: str) -> str:
        """Cache model file to disk"""

        cache_key = f"{model_id}/{filename}"
        cached_path = await self.disk_cache.store(cache_key, file_path)

        return cached_path

    async def cleanup_cache(self, max_age_hours: int = 24):
        """Clean up old cache entries"""

        # Clean memory cache (automatic with TTL)

        # Clean Redis cache
        pattern = "model:*"
        async for key in self.redis_client.scan_iter(match=pattern):
            ttl = await self.redis_client.ttl(key)
            if ttl < 0:  # No TTL set or expired
                await self.redis_client.delete(key)

        # Clean disk cache
        await self.disk_cache.cleanup(max_age_hours * 3600)

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""

        total_requests = (self.stats.memory_hits +
                         self.stats.redis_hits +
                         self.stats.cache_misses)

        return {
            "memory_cache": {
                "hits": self.stats.memory_hits,
                "size": len(self.memory_cache),
                "hit_rate": self.stats.memory_hits / total_requests if total_requests > 0 else 0
            },
            "redis_cache": {
                "hits": self.stats.redis_hits,
                "hit_rate": self.stats.redis_hits / total_requests if total_requests > 0 else 0
            },
            "file_cache": {
                "hits": self.stats.file_cache_hits,
                "misses": self.stats.file_cache_misses,
                "size_gb": await self.disk_cache.get_size() / (1024**3),
                "hit_rate": (self.stats.file_cache_hits /
                           (self.stats.file_cache_hits + self.stats.file_cache_misses)
                           if (self.stats.file_cache_hits + self.stats.file_cache_misses) > 0 else 0)
            },
            "overall": {
                "total_requests": total_requests,
                "cache_hit_rate": ((self.stats.memory_hits + self.stats.redis_hits) /
                                 total_requests if total_requests > 0 else 0)
            }
        }
```

This comprehensive technical implementation research provides the detailed architecture, code patterns, caching strategies, and optimization approaches needed to build a production-ready model catalog and repository management system that can efficiently handle thousands of models across multiple repositories while maintaining high performance and reliability.
